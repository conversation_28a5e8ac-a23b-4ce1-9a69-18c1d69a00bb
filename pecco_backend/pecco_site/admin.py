from django.contrib import admin
from . import models

class TranslationInlineMixin:
    extra = 0

class CategoryTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.CategoryTranslation

@admin.register(models.Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('slug','sort_order','is_active')
    list_editable = ('sort_order','is_active')
    inlines = [CategoryTranslationInline]

class ProductImageInline(admin.TabularInline):
    model = models.ProductImage
    extra = 1

class ProductTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.ProductTranslation

@admin.register(models.Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('id','chinese_name','categories_display','sort_order','is_active','tag_new','tag_hot','tag_featured')
    list_editable = ('sort_order','is_active','tag_new','tag_hot','tag_featured')
    inlines = [ProductImageInline, ProductTranslationInline]
    filter_horizontal = ('categories',)

    def chinese_name(self, obj):
        """显示中文名称"""
        return obj.get_chinese_name()
    chinese_name.short_description = '中文名称'

    def categories_display(self, obj):
        """显示所有分类"""
        return ', '.join([cat.slug for cat in obj.categories.all()])
    categories_display.short_description = '分类'

class CarouselTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.CarouselTranslation

@admin.register(models.CarouselItem)
class CarouselAdmin(admin.ModelAdmin):
    list_display = ('id','sort_order','is_active')
    list_editable = ('sort_order','is_active')
    inlines = [CarouselTranslationInline]

class PostTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.PostTranslation

@admin.register(models.Post)
class PostAdmin(admin.ModelAdmin):
    list_display = ('id','type','status','published_at')
    list_filter = ('type','status')
    inlines = [PostTranslationInline]

class TestimonialTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.TestimonialTranslation

@admin.register(models.Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    list_display = ('id','rating','is_active','sort_order')
    list_editable = ('rating','is_active','sort_order')
    inlines = [TestimonialTranslationInline]

class NavigationTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.NavigationTranslation

@admin.register(models.NavigationItem)
class NavigationItemAdmin(admin.ModelAdmin):
    list_display = ('label_key','chinese_label','english_label','type','target','is_active','sort_order')
    list_editable = ('type','target','is_active','sort_order')
    inlines = [NavigationTranslationInline]
    ordering = ('sort_order',)

    def chinese_label(self, obj):
        """显示中文标签"""
        trans = obj.translations.filter(locale='zh').first()
        return trans.label if trans else '-'
    chinese_label.short_description = '中文标签'

    def english_label(self, obj):
        """显示英文标签"""
        trans = obj.translations.filter(locale='en').first()
        return trans.label if trans else '-'
    english_label.short_description = '英文标签'

@admin.register(models.HomeLayoutBlock)
class HomeLayoutBlockAdmin(admin.ModelAdmin):
    list_display = ('block_type','is_active','sort_order')
    list_editable = ('is_active','sort_order')

@admin.register(models.ContactMessage)
class ContactMessageAdmin(admin.ModelAdmin):
    list_display = ('name', 'email', 'created_at', 'is_read', 'replied_at')
    list_filter = ('is_read', 'created_at')
    search_fields = ('name', 'email', 'message')
    readonly_fields = ('created_at', 'updated_at')
    list_editable = ('is_read',)
    ordering = ('-created_at',)

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'email', 'created_at')
        }),
        ('留言内容', {
            'fields': ('message',)
        }),
        ('处理状态', {
            'fields': ('is_read', 'replied_at')
        }),
    )

    def has_add_permission(self, request):
        # 不允许在后台手动添加留言
        return False

